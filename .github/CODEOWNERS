# The CODEOWNERS file defines individuals or teams that are automatically requested for
# review when someone opens a pull request that modifies certain code. When a draft pull
# request is marked as ready for review, code owners are automatically notified.
#
# See: https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners
#
# This is a comment.
# Each line is a file pattern followed by one or more owners.

# Global owners.
* @jimmyt857 @Michael-Equi @uzhilinsky

src/openpi/models/ @kvablack @uzhilinsky
src/openpi/training/ @kvablack @uzhilinsky

scripts/ @jimmyt857 @kvablack @uzhilinsky