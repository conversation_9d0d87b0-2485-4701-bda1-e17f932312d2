# This file was autogenerated by uv via the following command:
#    uv pip compile examples/simple_client/requirements.in -o examples/simple_client/requirements.txt --python-version 3.11.9
docstring-parser==0.16
    # via tyro
markdown-it-py==3.0.0
    # via rich
mdurl==0.1.2
    # via markdown-it-py
numpy==1.26.4
    # via -r examples/simple_client/requirements.in
polars==1.30.0
    # via -r examples/simple_client/requirements.in
pygments==2.19.1
    # via rich
rich==14.0.0
    # via
    #   -r examples/simple_client/requirements.in
    #   tyro
shtab==1.7.2
    # via tyro
tqdm==4.67.1
    # via -r examples/simple_client/requirements.in
typeguard==4.4.2
    # via tyro
typing-extensions==4.13.2
    # via
    #   typeguard
    #   tyro
tyro==0.9.22
    # via -r examples/simple_client/requirements.in
